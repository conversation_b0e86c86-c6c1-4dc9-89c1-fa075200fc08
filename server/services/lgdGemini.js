class LGDGeminiService {
  constructor() {
    this.client = null;
    this.initPromise = this.initializeClient();
  }

  async initializeClient() {
    if (!this.client) {
      const { GoogleGenAI } = await import('@google/genai');
      this.client = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
    }
    return this.client;
  }

  async generateResponse(model, prompt, config) {
    try {
      await this.initPromise; // Ensure client is initialized
      const response = await this.client.models.generateContent({
        model: model,
        contents: prompt,
        config: config
      });
      return response.text;
    } catch (error) {
      console.error('Error generating response:', error);
      throw new Error('Failed to generate response from Gemini');
    }
  }

  async runLGDPromptChain(input, analysisPrompt, formattingPrompt) {
    try {
      await this.initPromise; // Ensure client is initialized

      const { transcript, competencies } = input;

      // Step 1: Run LGD analysis prompt with transcript and competencies
      const analysisSystemPrompt = analysisPrompt.replace(
        '{{lgd_competencies}}',
        competencies
      )

      const analysisUserPrompt = `
        Here is the transcripts:
        ${transcript}
      `

      const analysisConfig = {
        temperature: 0.2,
        responseMimeType: "text/plain",
        systemInstruction: analysisSystemPrompt
      };

      console.log('Running LGD analysis prompt...');
      const analysisOutput = await this.generateResponse(
        'gemini-2.5-pro-preview-05-06',
        analysisUserPrompt,
        analysisConfig
      );

      // Step 2: Run formatting prompt with analysis output
      const formattingUserPrompt = `
        You need to format this text content to JSON format
        ${analysisOutput}
      `

      const formattingConfig = {
        temperature: 0,
        responseMimeType: "application/json",
        systemInstruction: formattingPrompt
      };

      console.log('Running LGD formatting prompt...');
      const finalOutput = await this.generateResponse(
        'gemini-2.5-flash-preview-05-20',
        formattingUserPrompt,
        formattingConfig
      );

      return {
        step1: {
          prompt: analysisUserPrompt,
          systemInstruction: analysisSystemPrompt,
          output: analysisOutput
        },
        step2: {
          prompt: formattingUserPrompt,
          systemInstruction: formattingPrompt,
          output: finalOutput
        },
        finalOutput
      };
    } catch (error) {
      console.error('Error in LGD prompt chain:', error);
      throw error;
    }
  }
}

module.exports = new LGDGeminiService();
